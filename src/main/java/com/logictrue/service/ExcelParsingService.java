package com.logictrue.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.entity.dto.ExcelTemplateSheetDTO;
import com.logictrue.iot.service.ExcelTemplateParserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel解析服务类
 * 基于JSON模板解析Excel文件，提取数据并进行格式验证
 */
public class ExcelParsingService {
    private static final Logger logger = LoggerFactory.getLogger(ExcelParsingService.class);

    private final ObjectMapper objectMapper;

    public ExcelParsingService() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 解析Excel文件
     *
     * @param excelFilePath    Excel文件路径
     * @param templateFilePath 模板JSON文件路径
     * @return 解析结果
     */
    public ExcelParsingResult parseExcelFile(String deviceId, String excelFilePath, String templateFilePath) {
        ExcelParsingResult result = new ExcelParsingResult();
        result.setExcelFilePath(excelFilePath);
        result.setTemplateFilePath(templateFilePath);
        result.setParseTime(LocalDateTime.now());


        //插入记录

        try {
            logger.info("开始解析Excel文件: {}", excelFilePath);
            logger.info("使用模板文件: {}", templateFilePath);

            // 读取模板配置
            JSONObject sheetConfig = loadTemplateConfig(templateFilePath);
            if (sheetConfig == null) {
                result.setSuccess(false);
                result.setErrorMessage("无法加载模板配置文件");
                return result;
            }

            DeviceDetectionData detectionData = new DeviceDetectionData();
            detectionData.setDeviceCode(deviceId);
            detectionData.setTemplateCode(sheetConfig.getString("templateCode"));
            detectionData.setTemplateName(sheetConfig.getString("templateName"));
            detectionData.setFilePath(excelFilePath);
            detectionData.setParseStatus(0); // 待解析

            // 更新解析状态为进行中
            detectionData.setParseStatus(0);
            detectionData.setParseTime(LocalDateTime.now());



            Map<String, List<ExcelTemplateCell>> cellsBySheet = sheetConfig.getObject("cellsBySheet", Map.class);

            Map<String, List<ExcelTemplateField>> fieldsBySheet = sheetConfig.getObject("fieldsBySheet", Map.class);


            // 打开Excel文件
            try (FileInputStream fis = new FileInputStream(excelFilePath);
                 Workbook workbook = new XSSFWorkbook(fis)) {

                DeviceDetectionDataService dataService = new DeviceDetectionDataService();

                Long detectionDataId = dataService.insertBeforeParsing(detectionData);

                int totalSheets = workbook.getNumberOfSheets();

                // 5. 处理每个Sheet
                for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                    Sheet sheet = workbook.getSheetAt(sheetIndex);
                    String sheetName = sheet.getSheetName();
                    String sheetId = ExcelTemplateParserService.getSheetIdByIndex(cellsBySheet, sheetIndex, sheetName);

                    List<ExcelTemplateCell> sheetCells = cellsBySheet.getOrDefault(sheetId, new ArrayList<>());
                    List<ExcelTemplateField> sheetFields = fieldsBySheet.getOrDefault(sheetId, new ArrayList<>());

                    try {
                        // 打印模板配置信息用于调试
                        logger.info("Sheet {} 的模板配置: 单元格数量={}, 字段数量={}", sheetName, sheetCells.size(), sheetFields.size());

                        // 解析基础字段
                        JSONObject basicFieldsResult = ExcelTemplateParserService.parseBasicFieldsFromSheet(detectionData.getId(), sheet, sheetIndex,
                                sheetId, sheetName, sheetCells, sheetFields);
                        // 保存到数据库
                        List<DeviceDetectionBasicField> basicFields = basicFieldsResult.getObject("basicFields", new TypeReference<List<DeviceDetectionBasicField>>() {});
                        if (basicFields != null && !basicFields.isEmpty()) {
                            dataService.batchInsertBasicFields(detectionDataId, basicFields);
                        }
                        int sheetBasicFields = basicFieldsResult.getIntValue("basicFieldCount");
                        //basicFieldsCount += sheetBasicFields;

                        // 解析表格数据
                        JSONObject tableDataResult = ExcelTemplateParserService.parseTableDataFromSheet(detectionData.getId(), sheet, sheetIndex,
                                sheetId, sheetName, sheetCells, sheetFields);
                        // 保存到数据库
                        List<DeviceDetectionTableHeader> tableHeaders = tableDataResult.getObject("tableHeaders", new TypeReference<List<DeviceDetectionTableHeader>>() {});
                        if (!CollectionUtils.isEmpty(tableHeaders)) {
                            //tableHeaderMapper.batchInsert(tableHeaders);
                        }
                        List<DeviceDetectionTableData> tableDataList = tableDataResult.getObject("tableDataList", new TypeReference<List<DeviceDetectionTableData>>() {});
                        if (!CollectionUtils.isEmpty(tableDataList)) {
                            //tableDataMapper.batchInsert(tableDataList);
                        }
                        int sheetTableRows = tableDataResult.getIntValue("tableDataCount");
                        //tableRowsCount += sheetTableRows;

                        //parsedSheets++;

                    } catch (Exception e) {
                        logger.error("解析Sheet {} 失败", sheetName, e);
                    }
                }
                // 解析Excel数据
                //parseWorkbook(workbook, templateConfig, result);

            }

            logger.info("Excel文件解析完成: {}", excelFilePath);

        } catch (Exception e) {
            logger.error("解析Excel文件失败: {}", excelFilePath, e);
            result.setSuccess(false);
            result.setErrorMessage("解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 加载模板配置
     */
    private JSONObject loadTemplateConfig(String templateFilePath) {
        try {
            Path templatePath = Paths.get(templateFilePath);
            if (!Files.exists(templatePath)) {
                logger.error("模板文件不存在: {}", templateFilePath);
                return null;
            }

            String templateContent = Files.readString(templatePath);

            JSONObject templateJson = JSONObject.parseObject(templateContent);

            JSONObject result = new JSONObject();
            List<ExcelTemplateSheetDTO> excelTemplateSheetDTOS = JSON.parseObject(templateJson.getString("sheetsConfig"), new TypeReference<>() {
            });
            Map<String, List<ExcelTemplateCell>> cellsBySheet = new HashMap<>();
            Map<String, List<ExcelTemplateField>> fieldsBySheet = new HashMap<>();

            excelTemplateSheetDTOS.forEach(excelTemplateSheetDTO -> {
                cellsBySheet.put(excelTemplateSheetDTO.getSheetId(), excelTemplateSheetDTO.getCells());
                fieldsBySheet.put(excelTemplateSheetDTO.getSheetId(), excelTemplateSheetDTO.getFields());
            });
            result.put("cellsBySheet", cellsBySheet);
            result.put("fieldsBySheet", fieldsBySheet);
            result.put("templateName", templateJson.getString("templateName"));
            result.put("templateCode", templateJson.getString("templateCode"));

            return result;

        } catch (Exception e) {
            logger.error("加载模板配置失败: {}", templateFilePath, e);
            return null;
        }
    }

    /**
     * 解析工作簿
     */
    private void parseWorkbook(Workbook workbook, JsonNode templateConfig, ExcelParsingResult result) {
        try {
            // 获取模板基本信息
            String templateName = templateConfig.path("templateName").asText();
            String templateCode = templateConfig.path("templateCode").asText();
            String deviceType = templateConfig.path("deviceType").asText();

            result.setTemplateName(templateName);
            result.setTemplateCode(templateCode);
            result.setDeviceType(deviceType);

            logger.info("解析模板信息: 名称={}, 编码={}, 设备类型={}", templateName, templateCode, deviceType);

            // 获取Sheet配置
            JsonNode sheetsConfig = templateConfig.path("sheetsConfig");
            if (!sheetsConfig.isArray()) {
                throw new IllegalArgumentException("模板配置中缺少有效的sheetsConfig");
            }

            List<SheetParsingResult> sheetResults = new ArrayList<>();

            // 解析每个Sheet
            for (JsonNode sheetConfig : sheetsConfig) {
                String sheetId = sheetConfig.path("sheetId").asText();
                String sheetName = sheetConfig.path("sheetName").asText();

                logger.info("开始解析Sheet: {} ({})", sheetName, sheetId);

                // 查找对应的Excel Sheet
                Sheet excelSheet = findSheet(workbook, sheetName, sheetId);
                if (excelSheet != null) {
                    SheetParsingResult sheetResult = parseSheet(excelSheet, sheetConfig);
                    sheetResults.add(sheetResult);
                    logger.info("Sheet解析完成: {}, 基础字段数: {}, 表格行数: {}",
                            sheetName, sheetResult.getBasicFields().size(), sheetResult.getTableData().size());
                } else {
                    logger.warn("未找到对应的Excel Sheet: {}", sheetName);
                }
            }

            result.setSheetResults(sheetResults);
            result.setSuccess(true);

            // 统计总数
            int totalBasicFields = sheetResults.stream().mapToInt(s -> s.getBasicFields().size()).sum();
            int totalTableRows = sheetResults.stream().mapToInt(s -> s.getTableData().size()).sum();

            logger.info("Excel解析完成: 总Sheet数={}, 总基础字段数={}, 总表格行数={}",
                    sheetResults.size(), totalBasicFields, totalTableRows);

        } catch (Exception e) {
            logger.error("解析工作簿失败", e);
            result.setSuccess(false);
            result.setErrorMessage("解析工作簿失败: " + e.getMessage());
        }
    }

    /**
     * 查找Excel中的Sheet
     */
    private Sheet findSheet(Workbook workbook, String sheetName, String sheetId) {
        // 首先按名称查找
        Sheet sheet = workbook.getSheet(sheetName);
        if (sheet != null) {
            return sheet;
        }

        // 如果按名称找不到，尝试按索引查找（假设sheetId是数字）
        try {
            int sheetIndex = Integer.parseInt(sheetId);
            if (sheetIndex >= 0 && sheetIndex < workbook.getNumberOfSheets()) {
                return workbook.getSheetAt(sheetIndex);
            }
        } catch (NumberFormatException e) {
            // sheetId不是数字，忽略
        }

        return null;
    }

    /**
     * 解析单个Sheet
     */
    private SheetParsingResult parseSheet(Sheet sheet, JsonNode sheetConfig) {
        SheetParsingResult result = new SheetParsingResult();
        result.setSheetName(sheet.getSheetName());
        result.setSheetId(sheetConfig.path("sheetId").asText());

        try {
            // 获取单元格配置
            JsonNode cellsConfig = sheetConfig.path("cells");
            JsonNode fieldsConfig = sheetConfig.path("fields");

            if (!cellsConfig.isArray() || !fieldsConfig.isArray()) {
                throw new IllegalArgumentException("Sheet配置中缺少有效的cells或fields配置");
            }

            // 解析基础字段
            Map<String, Object> basicFields = parseBasicFields(sheet, cellsConfig, fieldsConfig);
            result.setBasicFields(basicFields);

            // 解析表格数据
            List<Map<String, Object>> tableData = parseTableData(sheet, cellsConfig, fieldsConfig);
            result.setTableData(tableData);

            result.setSuccess(true);

        } catch (Exception e) {
            logger.error("解析Sheet失败: {}", sheet.getSheetName(), e);
            result.setSuccess(false);
            result.setErrorMessage("解析Sheet失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析基础字段
     */
    private Map<String, Object> parseBasicFields(Sheet sheet, JsonNode cellsConfig, JsonNode fieldsConfig) {
        Map<String, Object> basicFields = new HashMap<>();

        try {
            // 获取基础字段的单元格配置
            List<JsonNode> basicCells = new ArrayList<>();
            for (JsonNode cell : cellsConfig) {
                String cellType = cell.path("cellType").asText();
                if ("label".equals(cellType) || "value".equals(cellType)) {
                    basicCells.add(cell);
                }
            }

            // 按字段编码分组处理
            Map<String, List<JsonNode>> cellsByFieldCode = basicCells.stream()
                    .filter(cell -> cell.has("fieldCode") && !cell.path("fieldCode").asText().isEmpty())
                    .collect(Collectors.groupingBy(cell -> {
                        String fieldCode = cell.path("fieldCode").asText();
                        // 如果是value单元格，去掉_value后缀
                        if ("value".equals(cell.path("cellType").asText()) && fieldCode.endsWith("_value")) {
                            return fieldCode.substring(0, fieldCode.length() - 6);
                        }
                        return fieldCode;
                    }));

            // 处理每个基础字段
            for (Map.Entry<String, List<JsonNode>> entry : cellsByFieldCode.entrySet()) {
                String fieldCode = entry.getKey();
                List<JsonNode> fieldCells = entry.getValue();

                fieldCells.forEach(cell -> {
                    String cellType = cell.get("cellType").asText();
                    int rowIndex = cell.get("rowIndex").asInt();
                    int colIndex = cell.get("colIndex").asInt();
                    String cellValue = getCellValueAsString(sheet, rowIndex, colIndex);
                    logger.debug("基础字段单元格: {}, 行: {}, 列: {}", cellType, rowIndex, colIndex);
                    if ("label".equals(cellType)) {
                        if (basicFields.containsKey(fieldCode)) {

                        }
                    }
                });

                // 查找value单元格
                JsonNode valueCell = fieldCells.stream()
                        .filter(cell -> "value".equals(cell.path("cellType").asText()))
                        .findFirst()
                        .orElse(null);

                if (valueCell != null) {
                    int rowIndex = valueCell.path("rowIndex").asInt();
                    int colIndex = valueCell.path("colIndex").asInt();

                    String cellValue = getCellValueAsString(sheet, rowIndex, colIndex);
                    basicFields.put(fieldCode, cellValue);

                    logger.debug("解析基础字段: {} = {}", fieldCode, cellValue);
                }
            }

        } catch (Exception e) {
            logger.error("解析基础字段失败", e);
        }

        return basicFields;
    }

    /**
     * 解析表格数据
     */
    private List<Map<String, Object>> parseTableData(Sheet sheet, JsonNode cellsConfig, JsonNode fieldsConfig) {
        List<Map<String, Object>> tableData = new ArrayList<>();

        try {
            // 获取表头单元格配置
            List<JsonNode> headerCells = new ArrayList<>();
            for (JsonNode cell : cellsConfig) {
                String cellType = cell.path("cellType").asText();
                if ("header".equals(cellType)) {
                    headerCells.add(cell);
                }
            }

            if (headerCells.isEmpty()) {
                logger.info("未找到表头配置，跳过表格数据解析");
                return tableData;
            }

            // 按列索引排序表头
            headerCells.sort(Comparator.comparing(cell -> cell.path("colIndex").asInt()));

            // 获取数据起始行（表头行的下一行）
            int headerRowIndex = headerCells.get(0).path("rowIndex").asInt();
            int dataStartRow = headerRowIndex + 1;
            int lastRowNum = sheet.getLastRowNum();

            // 读取数据行
            for (int rowIndex = dataStartRow; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    break;
                }

                Map<String, Object> rowData = new HashMap<>();
                boolean isEmptyRow = true;

                // 读取每列数据
                for (JsonNode headerCell : headerCells) {
                    String fieldCode = headerCell.path("fieldCode").asText();
                    int colIndex = headerCell.path("colIndex").asInt();

                    String cellValue = getCellValueAsString(sheet, rowIndex, colIndex);
                    rowData.put(fieldCode, cellValue);

                    if (cellValue != null && !cellValue.trim().isEmpty()) {
                        isEmptyRow = false;
                    }
                }

                // 如果不是空行，添加到结果中
                if (!isEmptyRow) {
                    tableData.add(rowData);
                }
            }

        } catch (Exception e) {
            logger.error("解析表格数据失败", e);
        }

        return tableData;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Sheet sheet, int rowIndex, int colIndex) {
        try {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                return "";
            }

            Cell cell = row.getCell(colIndex);
            if (cell == null) {
                return "";
            }

            // 检查是否为合并单元格
            String mergedValue = getMergedCellValue(sheet, rowIndex, colIndex);
            if (mergedValue != null) {
                return mergedValue;
            }

            // 直接读取单元格值
            return getCellValueAsStringDirect(cell);

        } catch (Exception e) {
            logger.warn("读取单元格值失败，行: {}, 列: {}", rowIndex, colIndex, e);
            return "";
        }
    }

    /**
     * 获取合并单元格的值
     */
    private String getMergedCellValue(Sheet sheet, int rowIndex, int colIndex) {
        for (CellRangeAddress mergedRegion : sheet.getMergedRegions()) {
            if (mergedRegion.isInRange(rowIndex, colIndex)) {
                // 获取合并区域的第一个单元格的值
                Row firstRow = sheet.getRow(mergedRegion.getFirstRow());
                if (firstRow != null) {
                    Cell firstCell = firstRow.getCell(mergedRegion.getFirstColumn());
                    if (firstCell != null) {
                        return getCellValueAsStringDirect(firstCell);
                    }
                }
                break;
            }
        }
        return null;
    }

    /**
     * 直接获取单元格值作为字符串
     */
    private String getCellValueAsStringDirect(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，不显示小数点
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    try {
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == Math.floor(numericValue)) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    } catch (Exception e2) {
                        return "";
                    }
                }
            case BLANK:
            default:
                return "";
        }
    }

    /**
     * Excel解析结果类
     */
    public static class ExcelParsingResult {
        private String excelFilePath;
        private String templateFilePath;
        private LocalDateTime parseTime;
        private boolean success;
        private String errorMessage;
        private String templateName;
        private String templateCode;
        private String deviceType;
        private List<SheetParsingResult> sheetResults = new ArrayList<>();

        // Getters and Setters
        public String getExcelFilePath() {
            return excelFilePath;
        }

        public void setExcelFilePath(String excelFilePath) {
            this.excelFilePath = excelFilePath;
        }

        public String getTemplateFilePath() {
            return templateFilePath;
        }

        public void setTemplateFilePath(String templateFilePath) {
            this.templateFilePath = templateFilePath;
        }

        public LocalDateTime getParseTime() {
            return parseTime;
        }

        public void setParseTime(LocalDateTime parseTime) {
            this.parseTime = parseTime;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getTemplateName() {
            return templateName;
        }

        public void setTemplateName(String templateName) {
            this.templateName = templateName;
        }

        public String getTemplateCode() {
            return templateCode;
        }

        public void setTemplateCode(String templateCode) {
            this.templateCode = templateCode;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public List<SheetParsingResult> getSheetResults() {
            return sheetResults;
        }

        public void setSheetResults(List<SheetParsingResult> sheetResults) {
            this.sheetResults = sheetResults;
        }
    }

    /**
     * Sheet解析结果类
     */
    public static class SheetParsingResult {
        private String sheetId;
        private String sheetName;
        private boolean success;
        private String errorMessage;
        private Map<String, Object> basicFields = new HashMap<>();
        private List<Map<String, Object>> tableData = new ArrayList<>();

        // Getters and Setters
        public String getSheetId() {
            return sheetId;
        }

        public void setSheetId(String sheetId) {
            this.sheetId = sheetId;
        }

        public String getSheetName() {
            return sheetName;
        }

        public void setSheetName(String sheetName) {
            this.sheetName = sheetName;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Map<String, Object> getBasicFields() {
            return basicFields;
        }

        public void setBasicFields(Map<String, Object> basicFields) {
            this.basicFields = basicFields;
        }

        public List<Map<String, Object>> getTableData() {
            return tableData;
        }

        public void setTableData(List<Map<String, Object>> tableData) {
            this.tableData = tableData;
        }
    }
}
